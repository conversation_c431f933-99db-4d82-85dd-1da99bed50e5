namespace Kukui.Infrastructure.Authentication.Keycloak.SourceGenerator;

using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using ClassDeclarationOutput =
    (Microsoft.CodeAnalysis.CSharp.Syntax.ClassDeclarationSyntax ClassDeclaration, string ClassName, string ServiceName,
    string Namespace);
using BaseClassInfo =
    (string ClassName, string ServiceName, string Namespace, Microsoft.CodeAnalysis.INamedTypeSymbol ClassSymbol);
using ClassInfo =
    (Microsoft.CodeAnalysis.CSharp.Syntax.ClassDeclarationSyntax ClassDeclaration, string ClassName, string Namespace,
    Microsoft.CodeAnalysis.INamedTypeSymbol ClassSymbol);

[Generator(LanguageNames.CSharp)]
public class KeycloakAuthHandlerIncrementalGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // Step 1: Find all base classes with the [GenerateKeycloakAuthHandler] attribute
        var baseClassesWithAttribute = context.SyntaxProvider.CreateSyntaxProvider(
                static (s, _) => s is ClassDeclarationSyntax { AttributeLists.Count: > 0 },
                static (ctx, _) => GetBaseClassWithAttributeInfo(ctx))
            .Where(static m => m.ClassSymbol is not null)
            .Collect();

        // Step 2: Find all class declarations
        var allClassDeclarations = context.SyntaxProvider.CreateSyntaxProvider(
                static (s, _) => s is ClassDeclarationSyntax,
                static (ctx, _) => GetClassInfo(ctx))
            .Where(static m => m.ClassDeclaration is not null);

        // Step 3: Combine base classes with all classes to find derived classes
        var derivedClassesToGenerate = allClassDeclarations.Combine(baseClassesWithAttribute)
            .SelectMany(static (pair, _) =>
            {
                var (classInfo, baseClasses) = pair;
                var results = new List<ClassDeclarationOutput>();

                foreach (var baseClass in baseClasses)
                {
                    if (IsDerivedFrom(classInfo.ClassSymbol, baseClass.ClassSymbol) &&
                        !classInfo.ClassSymbol.IsAbstract)
                    {
                        results.Add(
                            (classInfo.ClassDeclaration, classInfo.ClassName, baseClass.ServiceName,
                                classInfo.Namespace));
                    }
                }

                return results;
            });

        // Step 4: Generate handlers for all derived classes
        context.RegisterSourceOutput(
            derivedClassesToGenerate,
            static (spc, info) => GenerateHandler(spc, info.ClassName!, info.Namespace));
    }

    private static BaseClassInfo GetBaseClassWithAttributeInfo(GeneratorSyntaxContext context)
    {
        var classDeclaration = (ClassDeclarationSyntax) context.Node;
        var hasAttribute = false;
        var className = classDeclaration.Identifier.Text;
        var serviceName = className;

        foreach (var attribute in classDeclaration.AttributeLists.SelectMany(x => x.Attributes))
        {
            var attributeSymbol = context.SemanticModel.GetSymbolInfo(attribute).Symbol;
            if (attributeSymbol == null) continue;

            var containingType = attributeSymbol.ContainingType;
            if (containingType == null) continue;

            if (containingType.Name == nameof(GenerateKeycloakAuthHandlerAttribute))
            {
                hasAttribute = true;
                serviceName = GetServiceNameFromAttribute(context, attribute) ?? className;
                break;
            }
        }

        if (!hasAttribute)
        {
            return (null!, null!, null!, null!);
        }

        var symbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration) as INamedTypeSymbol;
        if (symbol == null)
        {
            return (null!, null!, null!, null!);
        }

        var namespaceName = GetNamespaceName(classDeclaration, symbol);
        return (className, serviceName, namespaceName, symbol);
    }

    private static ClassInfo GetClassInfo(GeneratorSyntaxContext context)
    {
        var classDeclaration = (ClassDeclarationSyntax) context.Node;
        var className = classDeclaration.Identifier.Text;
        var symbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration) as INamedTypeSymbol;

        if (symbol == null)
        {
            return (null!, null!, null!, null!);
        }

        var namespaceName = GetNamespaceName(classDeclaration, symbol);
        return (classDeclaration, className, namespaceName, symbol);
    }

    private static bool IsDerivedFrom(INamedTypeSymbol derivedClass, INamedTypeSymbol baseClass)
    {
        if (derivedClass == null || baseClass == null) return false;

        if (SymbolEqualityComparer.Default.Equals(derivedClass, baseClass)) return true;

        var current = derivedClass.BaseType;
        while (current != null)
        {
            if (SymbolEqualityComparer.Default.Equals(current, baseClass)) return true;

            current = current.BaseType;
        }

        return false;
    }

    private static string GetNamespaceName(ClassDeclarationSyntax classDeclaration, INamedTypeSymbol symbol)
    {
        var namespaceName = symbol.ContainingNamespace?.ToDisplayString();

        if (string.IsNullOrEmpty(namespaceName))
        {
            var syntaxRoot = classDeclaration.SyntaxTree.GetRoot();

            var fileScopedNamespace = syntaxRoot.DescendantNodes()
                .OfType<FileScopedNamespaceDeclarationSyntax>()
                .FirstOrDefault();
            if (fileScopedNamespace != null)
            {
                namespaceName = fileScopedNamespace.Name.ToString();
            }
            else
            {
                var regularNamespace =
                    syntaxRoot.DescendantNodes().OfType<NamespaceDeclarationSyntax>().FirstOrDefault();
                namespaceName = regularNamespace?.Name.ToString();
            }
        }

        return namespaceName ?? throw new InvalidOperationException(
            $"Unable to resolve namespace for class '{classDeclaration.Identifier.Text}'");
    }

    private static void GenerateHandler(SourceProductionContext context, string className, string namespaceName)
    {
        if (string.IsNullOrEmpty(className)) return;

        var sourceText = $$"""
                           // <auto-generated/>
                           using System.Net.Http;
                           using Kukui.Infrastructure.Hosting.Common.Models;
                           using Kukui.Infrastructure.Authentication.Keycloak;
                           using Microsoft.Extensions.Caching.Memory;

                           namespace {{namespaceName}}
                           {
                               public class {{className}}AuthenticationHandler : KeycloakAuthenticationHandler
                               {
                                   public {{className}}AuthenticationHandler(
                                       HttpClient client,
                                       IMemoryCache memoryCache,
                                       ServerConfiguration configuration) : base(client, memoryCache, configuration)
                                   {
                                   }

                                   protected override string ServiceName { get; set; } = "{{className}}";
                               }
                           }
                           """;

        context.AddSource($"{className}AuthenticationHandler.g.cs", sourceText);
    }

    private static string? GetServiceNameFromAttribute(GeneratorSyntaxContext context, AttributeSyntax attribute)
    {
        // Default to null if not specified
        string? serviceName = null;

        // Check for constructor arguments (positional parameters)
        if (attribute.ArgumentList?.Arguments.Count > 0)
        {
            var firstArg = attribute.ArgumentList.Arguments[0];

            // Get the constant value from the semantic model
            var constantValue = context.SemanticModel.GetConstantValue(firstArg.Expression);
            if (constantValue.HasValue && constantValue.Value is string value)
            {
                serviceName = value;
            }
        }

        // Check for named arguments
        foreach (var arg in attribute.ArgumentList?.Arguments ?? Enumerable.Empty<AttributeArgumentSyntax>())
        {
            if (arg.NameEquals != null && arg.NameEquals.Name.Identifier.Text == "ServiceName")
            {
                var constantValue = context.SemanticModel.GetConstantValue(arg.Expression);
                if (constantValue.HasValue && constantValue.Value is string value)
                {
                    serviceName = value;
                }
            }
        }

        return serviceName;
    }
}
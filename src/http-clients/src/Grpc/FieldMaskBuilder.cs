namespace Kukui.Infrastructure.Grpc
{
    using System.Collections;
    using System.Collections.Concurrent;
    using System.Linq.Expressions;
    using System.Reflection;
    using System.Text;
    using Google.Protobuf;
    using Google.Protobuf.Reflection;
    using Google.Protobuf.WellKnownTypes;
    using Type = System.Type;

    /// <summary>
    /// Provides functionality to build gRPC FieldMask objects from strongly-typed lambda expressions.
    /// </summary>
    public static class FieldMaskBuilder
    {
        // Cache for message descriptors to avoid repeated instantiation
        private static readonly ConcurrentDictionary<Type, MessageDescriptor> DescriptorCache = new();

        // Pre-computed type checks for better performance
        private static readonly Type MessageType = typeof(IMessage);
        private static readonly Type ListType = typeof(IList);

        /// <summary>
        /// Creates a FieldMask using field names from a strongly-typed expression.
        /// </summary>
        /// <typeparam name="TMessage">The protobuf message type that implements IMessage.</typeparam>
        /// <param name="fieldMaskExpression">
        /// A lambda expression that selects fields using anonymous object syntax.
        /// Example: x => new { x.OrderNumber, x.Customer.Name, x.Vehicles }
        /// </param>
        /// <returns>A normalized FieldMask containing the specified field paths.</returns>
        /// <exception cref="ArgumentNullException">Thrown when fieldMaskExpression is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the expression format is invalid.</exception>
        public static FieldMask CreatePath<TMessage>(Expression<Func<TMessage, object>> fieldMaskExpression)
            where TMessage : IMessage, new()
        {
            return CreateFieldMask<TMessage>(fieldMaskExpression, field => field.Name);
        }

        /// <summary>
        /// Creates a FieldMask using field numbers from a strongly-typed expression.
        /// </summary>
        /// <typeparam name="TMessage">The protobuf message type that implements IMessage.</typeparam>
        /// <param name="fieldMaskExpression">
        /// A lambda expression that selects fields using anonymous object syntax.
        /// Example: x => new { x.OrderNumber, x.Customer.Name, x.Vehicles }
        /// </param>
        /// <returns>A normalized FieldMask containing the specified field paths using field numbers.</returns>
        /// <exception cref="ArgumentNullException">Thrown when fieldMaskExpression is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the expression format is invalid.</exception>
        public static FieldMask CreatePathFromFieldNumbers<TMessage>(Expression<Func<TMessage, object>> fieldMaskExpression)
            where TMessage : IMessage, new()
        {
            return CreateFieldMask<TMessage>(fieldMaskExpression, field => field.FieldNumber);
        }

        /// <summary>
        /// Core method that creates a FieldMask using the specified field selector function.
        /// </summary>
        private static FieldMask CreateFieldMask<TMessage>(
            Expression<Func<TMessage, object>> fieldMaskExpression,
            Func<FieldDescriptor, object> fieldSelector)
            where TMessage : IMessage, new()
        {
            if (fieldMaskExpression == null)
                throw new ArgumentNullException(nameof(fieldMaskExpression));

            var descriptor = GetMessageDescriptor<TMessage>();
            var fieldPaths = new List<string>();

            if (fieldMaskExpression.Body is not NewExpression newExpression)
            {
                throw new InvalidOperationException(
                    "Expression must use anonymous object syntax. " +
                    "Example: x => new { x.OrderNumber, x.Customer.Name, x.Vehicles }");
            }

            foreach (var argument in newExpression.Arguments)
            {
                if (argument is not MemberExpression memberExpression)
                {
                    throw new InvalidOperationException(
                        "Only member expressions are allowed in the anonymous object. " +
                        "Example: x => new { x.OrderNumber, x.Vehicles, x.Customer.Contacts.Address.Coordinates }");
                }

                var typePath = new Stack<Type>();
                BuildExpressionTypePath(memberExpression, typePath);

                var fieldPath = FindFieldRecursive(descriptor, memberExpression, typePath, fieldSelector);
                if (fieldPath == null)
                {
                    throw new InvalidOperationException(
                        $"Could not resolve field path for member '{memberExpression.Member.Name}'. " +
                        "Ensure the member exists in the protobuf message definition.");
                }

                fieldPaths.Add(fieldPath);
            }

            return FieldMask.FromString(string.Join(",", fieldPaths)).Normalize();
        }

        /// <summary>
        /// Gets the message descriptor for the specified type, using caching for performance.
        /// </summary>
        private static MessageDescriptor GetMessageDescriptor<TMessage>() where TMessage : IMessage, new()
        {
            return DescriptorCache.GetOrAdd(typeof(TMessage), _ => new TMessage().Descriptor);
        }

        /// <summary>
        /// Recursively finds the field path in the message descriptor hierarchy.
        /// </summary>
        private static string FindFieldRecursive(
            MessageDescriptor messageDescriptor,
            MemberExpression memberExpression,
            Stack<Type> expressionTypePath,
            Func<FieldDescriptor, object> selector)
        {
            foreach (var field in messageDescriptor.Fields.InFieldNumberOrder())
            {
                // Check if this field matches the current member expression
                if (field.PropertyName == memberExpression.Member.Name &&
                    field.ContainingType.ClrType == memberExpression.Expression?.Type)
                {
                    return selector(field).ToString();
                }

                // Continue searching in nested message fields
                if (field.FieldType == FieldType.Message &&
                    expressionTypePath.Count > 0 &&
                    field.MessageType.ClrType == expressionTypePath.Peek())
                {
                    expressionTypePath.Pop();
                    var subPath = FindFieldRecursive(field.MessageType, memberExpression, expressionTypePath, selector);
                    if (subPath != null)
                    {
                        return $"{selector(field)}.{subPath}";
                    }
                    // Restore the stack state for backtracking
                    expressionTypePath.Push(field.MessageType.ClrType);
                }
            }

            return null!;
        }

        /// <summary>
        /// Builds the type path for the given member expression using a stack for efficient traversal.
        /// </summary>
        private static void BuildExpressionTypePath(MemberExpression expression, Stack<Type> path)
        {
            if (expression?.Expression is ParameterExpression parameterExpression)
            {
                var memberType = GetMemberType(expression);
                var targetType = ShouldTrackType(memberType) ? GetElementType(memberType) : parameterExpression.Type;
                path.Push(targetType);
                return;
            }

            if (expression?.Expression is MemberExpression parentMemberExpression)
            {
                BuildExpressionTypePath(parentMemberExpression, path);
                var memberType = GetMemberType(expression);
                if (ShouldTrackType(memberType))
                {
                    path.Push(GetElementType(memberType));
                }
                return;
            }

            if (expression?.Expression is MethodCallExpression methodCallExpression &&
                methodCallExpression.Object is MemberExpression methodMemberExpression)
            {
                BuildExpressionTypePath(methodMemberExpression, path);
                var memberType = GetMemberType(expression);
                if (ShouldTrackType(memberType))
                {
                    path.Push(GetElementType(memberType));
                }
            }
        }

        /// <summary>
        /// Gets the type of a member expression, handling both properties and fields.
        /// </summary>
        private static Type GetMemberType(MemberExpression expression)
        {
            return expression.Member switch
            {
                PropertyInfo propertyInfo => propertyInfo.PropertyType,
                FieldInfo fieldInfo => fieldInfo.FieldType,
                _ => expression.Type
            };
        }

        /// <summary>
        /// Determines if a type should be tracked in the path (IMessage or IList types).
        /// </summary>
        private static bool ShouldTrackType(Type type)
        {
            return MessageType.IsAssignableFrom(type) || ListType.IsAssignableFrom(type);
        }

        /// <summary>
        /// Gets the element type for generic collections or the type itself for non-generic types.
        /// </summary>
        private static Type GetElementType(Type type)
        {
            return type.IsGenericType ? type.GetGenericArguments()[0] : type;
        }
    }
}
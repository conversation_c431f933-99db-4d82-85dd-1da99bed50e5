namespace Kukui.Infrastructure.Grpc
{
    using System.Collections;
    using System.Linq.Expressions;
    using System.Reflection;
    using Google.Protobuf;
    using Google.Protobuf.Reflection;
    using Google.Protobuf.WellKnownTypes;
    using Type = System.Type;

    public class FieldMaskBuilder
    {
        public static FieldMask CreatePath<TMessage>(Expression<Func<TMessage, object>> fieldMaskExpression)
            where TMessage : IMessage, new()
        {
            var descriptor = new TMessage().Descriptor;
            var fieldMask = new List<string>();

            if (fieldMaskExpression.Body is NewExpression newExpression)
            {
                foreach (var argument in newExpression.Arguments)
                {
                    var queue = new Queue<Type>();
                    GetExpressionTypePath((MemberExpression)argument, queue);
                    var fieldPath = FindFieldRecursive(descriptor, (MemberExpression)argument, queue, f => f.Name);
                    fieldMask.Add(fieldPath);
                }
            }

            return FieldMask.FromString(string.Join(",", fieldMask)).Normalize();
        }

        public static FieldMask CreatePathFromFieldNumbers<TMessage>(Expression<Func<TMessage, object>> fieldMaskExpression)
            where TMessage : IMessage, new()
        {
            var descriptor = new TMessage().Descriptor;
            var fieldMask = new List<string>();

            if (fieldMaskExpression.Body is NewExpression newExpression)
            {
                foreach (var argument in newExpression.Arguments)
                {
                    if (argument is not MemberExpression memberExpression)
                        throw new InvalidOperationException(
                            "Only member expression are allowed.For example: \"x => new {x.OrderNumber, x.Vehicles, x.Customer.Contacts.Address.Coordinates}\"");
                    var path = new Queue<Type>();
                    GetExpressionTypePath(memberExpression, path);
                    var fieldPath = FindFieldRecursive(descriptor, memberExpression, path, f => f.FieldNumber);
                    fieldMask.Add(fieldPath);
                }
            }

            return FieldMask.FromString(string.Join(",", fieldMask)).Normalize();
        }

        private static string FindFieldRecursive(MessageDescriptor messageDescriptor, MemberExpression memberExpression,
            Queue<Type> expressionTypePath, Func<FieldDescriptor, object> selector)
        {
            foreach (var field in messageDescriptor.Fields.InFieldNumberOrder())
            {
                if (field.PropertyName == memberExpression.Member.Name &&
                    field.ContainingType.ClrType == memberExpression.Expression?.Type)
                {
                    return selector.Invoke(field).ToString();
                }

                if (field.FieldType != FieldType.Message) continue;
                if (!expressionTypePath.Any() || field.MessageType.ClrType != expressionTypePath.Peek()) continue;
                expressionTypePath.Dequeue();
                var subPath = FindFieldRecursive(field.MessageType, memberExpression, expressionTypePath, selector);
                if (subPath == null) continue;
                subPath = $"{selector.Invoke(field)}.{subPath}";
                return subPath;
            }

            return null;
        }

        private static void GetExpressionTypePath(MemberExpression expression, Queue<Type> path)
        {
            if (expression?.Expression is ParameterExpression parameterExpression)
            {
                var type = typeof(IMessage).IsAssignableFrom(((PropertyInfo) expression.Member).PropertyType)
                           || typeof(IList).IsAssignableFrom(expression.Type)
                    ? ((PropertyInfo)expression.Member).PropertyType.IsGenericType
                        ? ((PropertyInfo)expression.Member).PropertyType.GetGenericArguments()[0]
                        : ((PropertyInfo)expression.Member).PropertyType
                    : parameterExpression.Type;
                path.Enqueue(type);
                return;
            }

            if (expression?.Expression is MemberExpression memberExpression)
            {
                GetExpressionTypePath(memberExpression, path);
                if (typeof(IMessage).IsAssignableFrom(expression.Type) || typeof(IList).IsAssignableFrom(expression.Type))
                    path.Enqueue(expression.Type.IsGenericType
                        ? expression.Type.GetGenericArguments()[0]
                        : expression.Type);
                return;
            }

            if (expression?.Expression is MethodCallExpression methodCallExpression)
            {
                GetExpressionTypePath(methodCallExpression.Object as MemberExpression, path);
                if (typeof(IMessage).IsAssignableFrom(expression.Type) ||
                    typeof(IList).IsAssignableFrom(expression.Type))
                    path.Enqueue(expression.Type.IsGenericType
                        ? expression.Type.GetGenericArguments()[0]
                        : expression.Type);
            }
        }
    }
}